using System;
using System.Collections.Generic;
using System.Data;
using System.Windows.Forms;

namespace MariaDBManager
{
    public partial class MainForm : Form
    {
        private DatabaseHelper dbHelper;
        private DataGridView dataGridView;
        private DataTable originalData;
        private Button btnAdd, btnEdit, btnDelete, btnRefresh;
        private TextBox txtConnectionString;
        private Label lblConnectionString;
        private ComboBox cmbTables;
        private Label lblTable;
        private TextBox txtSearch;
        private Label lblSearch;
        private Button btnClearSearch;

        public MainForm()
        {
            InitializeComponent();
            dbHelper = new DatabaseHelper();
        }

        private void InitializeComponent()
        {
            this.Text = "Veteran's Voices Data Manager";
            this.Size = new System.Drawing.Size(1000, 600);
            this.StartPosition = FormStartPosition.CenterScreen;

            // Connection string controls
            lblConnectionString = new Label()
            {
                Text = "Connection String:",
                Location = new System.Drawing.Point(12, 15),
                Size = new System.Drawing.Size(120, 23),
                TextAlign = System.Drawing.ContentAlignment.MiddleLeft
            };

            txtConnectionString = new TextBox()
            {
                Location = new System.Drawing.Point(140, 12),
                Size = new System.Drawing.Size(500, 23),
                Text = "Server=localhost;Database=vvwp_live;Uid=root;Pwd=;"
            };

            // Table selection
            lblTable = new Label()
            {
                Text = "Table:",
                Location = new System.Drawing.Point(650, 15),
                Size = new System.Drawing.Size(50, 23),
                TextAlign = System.Drawing.ContentAlignment.MiddleLeft
            };

            cmbTables = new ComboBox()
            {
                Location = new System.Drawing.Point(710, 12),
                Size = new System.Drawing.Size(150, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // Buttons
            btnRefresh = new Button()
            {
                Text = "Connect & Load Tables",
                Location = new System.Drawing.Point(870, 12),
                Size = new System.Drawing.Size(120, 23)
            };

            btnAdd = new Button()
            {
                Text = "Add Record",
                Location = new System.Drawing.Point(12, 50),
                Size = new System.Drawing.Size(100, 30)
            };

            btnEdit = new Button()
            {
                Text = "Edit Record",
                Location = new System.Drawing.Point(120, 50),
                Size = new System.Drawing.Size(100, 30)
            };

            btnDelete = new Button()
            {
                Text = "Delete Record",
                Location = new System.Drawing.Point(228, 50),
                Size = new System.Drawing.Size(100, 30)
            };

            // Search controls
            lblSearch = new Label()
            {
                Text = "Search:",
                Location = new Point(350, 55),
                Size = new Size(50, 23),
                TextAlign = System.Drawing.ContentAlignment.MiddleLeft
            };

            txtSearch = new TextBox()
            {
                Location = new Point(405, 52),
                Size = new Size(200, 23),
                PlaceholderText = "Type to search..."
            };

            btnClearSearch = new Button()
            {
                Text = "Clear",
                Location = new Point(615, 52),
                Size = new Size(60, 23)
            };

            // DataGridView
            dataGridView = new DataGridView()
            {
                Location = new System.Drawing.Point(12, 90),
                Size = new System.Drawing.Size(960, 460),
                Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right,
                AllowUserToAddRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true
            };

            // Add controls to form
            this.Controls.AddRange(new Control[] {
                lblConnectionString, txtConnectionString,
                lblTable, cmbTables,
                btnRefresh, btnAdd, btnEdit, btnDelete,
                lblSearch, txtSearch, btnClearSearch,
                dataGridView
            });

            // Event handlers
            btnRefresh.Click += BtnRefresh_Click;
            btnAdd.Click += BtnAdd_Click;
            btnEdit.Click += BtnEdit_Click;
            btnDelete.Click += BtnDelete_Click;
            cmbTables.SelectedIndexChanged += CmbTables_SelectedIndexChanged;
            txtSearch.TextChanged += TxtSearch_TextChanged;
            btnClearSearch.Click += BtnClearSearch_Click;
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            try
            {
                dbHelper.ConnectionString = txtConnectionString.Text;
                var tables = dbHelper.GetTables();
                
                cmbTables.Items.Clear();
                foreach (string table in tables)
                {
                    cmbTables.Items.Add(table);
                }

                if (cmbTables.Items.Count > 0)
                {
                    cmbTables.SelectedIndex = 0;
                }

                MessageBox.Show("Connected successfully!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Connection failed: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CmbTables_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbTables.SelectedItem != null)
            {
                LoadTableData();
            }
        }

        private void LoadTableData()
        {
            try
            {
                string tableName = cmbTables.SelectedItem.ToString();
                originalData = dbHelper.GetTableData(tableName);
                dataGridView.DataSource = originalData;

                // Clear search when loading new table data
                txtSearch.Text = "";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            if (cmbTables.SelectedItem == null)
            {
                MessageBox.Show("Please select a table first.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            string tableName = cmbTables.SelectedItem.ToString();
            var columns = dbHelper.GetTableColumns(tableName);

            using (var form = new DataEntryForm(columns, null, dbHelper, tableName))
            {
                if (form.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        dbHelper.InsertRecord(tableName, form.GetValues());
                        LoadTableData();
                        MessageBox.Show("Record added successfully!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Error adding record: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (dataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("Please select a record to edit.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            string tableName = cmbTables.SelectedItem.ToString();
            var columns = dbHelper.GetTableColumns(tableName);
            var selectedRow = dataGridView.SelectedRows[0];
            
            // Get current values
            var currentValues = new Dictionary<string, object>();
            foreach (DataGridViewCell cell in selectedRow.Cells)
            {
                currentValues[cell.OwningColumn.Name] = cell.Value;
            }

            using (var form = new DataEntryForm(columns, currentValues, dbHelper, tableName))
            {
                if (form.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        var primaryKey = dbHelper.GetPrimaryKey(tableName);
                        var keyValue = currentValues[primaryKey];
                        
                        dbHelper.UpdateRecord(tableName, primaryKey, keyValue, form.GetValues());
                        LoadTableData();
                        MessageBox.Show("Record updated successfully!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Error updating record: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            if (dataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("Please select a record to delete.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (MessageBox.Show("Are you sure you want to delete this record?", "Confirm Delete", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                try
                {
                    string tableName = cmbTables.SelectedItem.ToString();
                    var selectedRow = dataGridView.SelectedRows[0];
                    var primaryKey = dbHelper.GetPrimaryKey(tableName);
                    var keyValue = selectedRow.Cells[primaryKey].Value;

                    dbHelper.DeleteRecord(tableName, primaryKey, keyValue);
                    LoadTableData();
                    MessageBox.Show("Record deleted successfully!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error deleting record: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            if (originalData == null) return;

            try
            {
                string searchText = txtSearch.Text.Trim();

                if (string.IsNullOrEmpty(searchText))
                {
                    // Show all data when search is empty
                    dataGridView.DataSource = originalData;
                }
                else
                {
                    // Create filter expression for all columns
                    var filterParts = new List<string>();

                    foreach (DataColumn column in originalData.Columns)
                    {
                        // Handle different data types for searching
                        if (column.DataType == typeof(string))
                        {
                            filterParts.Add($"CONVERT([{column.ColumnName}], 'System.String') LIKE '%{searchText.Replace("'", "''")}%'");
                        }
                        else
                        {
                            filterParts.Add($"CONVERT([{column.ColumnName}], 'System.String') LIKE '%{searchText.Replace("'", "''")}%'");
                        }
                    }

                    string filter = string.Join(" OR ", filterParts);

                    // Create a filtered view
                    DataView dataView = new DataView(originalData);
                    dataView.RowFilter = filter;
                    dataGridView.DataSource = dataView;
                }
            }
            catch (Exception ex)
            {
                // If filtering fails, show all data
                dataGridView.DataSource = originalData;
                // Optionally show error message for debugging
                // MessageBox.Show($"Search error: {ex.Message}", "Search Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void BtnClearSearch_Click(object sender, EventArgs e)
        {
            txtSearch.Text = "";
            if (originalData != null)
            {
                dataGridView.DataSource = originalData;
            }
        }
    }
}
