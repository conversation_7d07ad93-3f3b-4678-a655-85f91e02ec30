using System;
using System.Collections.Generic;
using System.Data;
using System.Windows.Forms;

namespace MariaDBManager
{
    public partial class MainForm : Form
    {
        private DatabaseHelper dbHelper;
        private DataGridView dataGridView;
        private Button btnAdd, btnEdit, btnDelete, btnRefresh;
        private TextBox txtConnectionString;
        private Label lblConnectionString;
        private ComboBox cmbTables;
        private Label lblTable;

        public MainForm()
        {
            InitializeComponent();
            dbHelper = new DatabaseHelper();
        }

        private void InitializeComponent()
        {
            this.Text = "MariaDB Manager";
            this.Size = new System.Drawing.Size(1000, 600);
            this.StartPosition = FormStartPosition.CenterScreen;

            // Connection string controls
            lblConnectionString = new Label()
            {
                Text = "Connection String:",
                Location = new System.Drawing.Point(12, 15),
                Size = new System.Drawing.Size(120, 23),
                TextAlign = System.Drawing.ContentAlignment.MiddleLeft
            };

            txtConnectionString = new TextBox()
            {
                Location = new System.Drawing.Point(140, 12),
                Size = new System.Drawing.Size(500, 23),
                Text = "Server=localhost;Database=testdb;Uid=root;Pwd=password;"
            };

            // Table selection
            lblTable = new Label()
            {
                Text = "Table:",
                Location = new System.Drawing.Point(650, 15),
                Size = new System.Drawing.Size(50, 23),
                TextAlign = System.Drawing.ContentAlignment.MiddleLeft
            };

            cmbTables = new ComboBox()
            {
                Location = new System.Drawing.Point(710, 12),
                Size = new System.Drawing.Size(150, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // Buttons
            btnRefresh = new Button()
            {
                Text = "Connect & Load Tables",
                Location = new System.Drawing.Point(870, 12),
                Size = new System.Drawing.Size(120, 23)
            };

            btnAdd = new Button()
            {
                Text = "Add Record",
                Location = new System.Drawing.Point(12, 50),
                Size = new System.Drawing.Size(100, 30)
            };

            btnEdit = new Button()
            {
                Text = "Edit Record",
                Location = new System.Drawing.Point(120, 50),
                Size = new System.Drawing.Size(100, 30)
            };

            btnDelete = new Button()
            {
                Text = "Delete Record",
                Location = new System.Drawing.Point(228, 50),
                Size = new System.Drawing.Size(100, 30)
            };

            // DataGridView
            dataGridView = new DataGridView()
            {
                Location = new System.Drawing.Point(12, 90),
                Size = new System.Drawing.Size(960, 460),
                Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right,
                AllowUserToAddRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true
            };

            // Add controls to form
            this.Controls.AddRange(new Control[] {
                lblConnectionString, txtConnectionString,
                lblTable, cmbTables,
                btnRefresh, btnAdd, btnEdit, btnDelete,
                dataGridView
            });

            // Event handlers
            btnRefresh.Click += BtnRefresh_Click;
            btnAdd.Click += BtnAdd_Click;
            btnEdit.Click += BtnEdit_Click;
            btnDelete.Click += BtnDelete_Click;
            cmbTables.SelectedIndexChanged += CmbTables_SelectedIndexChanged;
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            try
            {
                dbHelper.ConnectionString = txtConnectionString.Text;
                var tables = dbHelper.GetTables();
                
                cmbTables.Items.Clear();
                foreach (string table in tables)
                {
                    cmbTables.Items.Add(table);
                }

                if (cmbTables.Items.Count > 0)
                {
                    cmbTables.SelectedIndex = 0;
                }

                MessageBox.Show("Connected successfully!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Connection failed: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CmbTables_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbTables.SelectedItem != null)
            {
                LoadTableData();
            }
        }

        private void LoadTableData()
        {
            try
            {
                string tableName = cmbTables.SelectedItem.ToString();
                DataTable data = dbHelper.GetTableData(tableName);
                dataGridView.DataSource = data;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            if (cmbTables.SelectedItem == null)
            {
                MessageBox.Show("Please select a table first.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            string tableName = cmbTables.SelectedItem.ToString();
            var columns = dbHelper.GetTableColumns(tableName);
            
            using (var form = new DataEntryForm(columns, null))
            {
                if (form.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        dbHelper.InsertRecord(tableName, form.GetValues());
                        LoadTableData();
                        MessageBox.Show("Record added successfully!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Error adding record: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (dataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("Please select a record to edit.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            string tableName = cmbTables.SelectedItem.ToString();
            var columns = dbHelper.GetTableColumns(tableName);
            var selectedRow = dataGridView.SelectedRows[0];
            
            // Get current values
            var currentValues = new Dictionary<string, object>();
            foreach (DataGridViewCell cell in selectedRow.Cells)
            {
                currentValues[cell.OwningColumn.Name] = cell.Value;
            }

            using (var form = new DataEntryForm(columns, currentValues))
            {
                if (form.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        var primaryKey = dbHelper.GetPrimaryKey(tableName);
                        var keyValue = currentValues[primaryKey];
                        
                        dbHelper.UpdateRecord(tableName, primaryKey, keyValue, form.GetValues());
                        LoadTableData();
                        MessageBox.Show("Record updated successfully!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Error updating record: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            if (dataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("Please select a record to delete.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (MessageBox.Show("Are you sure you want to delete this record?", "Confirm Delete", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                try
                {
                    string tableName = cmbTables.SelectedItem.ToString();
                    var selectedRow = dataGridView.SelectedRows[0];
                    var primaryKey = dbHelper.GetPrimaryKey(tableName);
                    var keyValue = selectedRow.Cells[primaryKey].Value;

                    dbHelper.DeleteRecord(tableName, primaryKey, keyValue);
                    LoadTableData();
                    MessageBox.Show("Record deleted successfully!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error deleting record: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
    }
}
