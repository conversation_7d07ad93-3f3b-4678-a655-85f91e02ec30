# Real-Time Search Feature

## Overview
The MariaDB Manager now includes a powerful real-time search feature that filters table data as you type, making it easy to find specific records in large datasets.

## Features

### ✨ **Real-Time Filtering**
- **Instant Results**: Data filters as you type, no need to press Enter
- **Multi-Column Search**: Searches across all columns simultaneously
- **Case-Insensitive**: Finds matches regardless of case
- **Partial Matching**: Finds records containing your search term anywhere in the data

### 🎯 **Smart Search**
- **All Data Types**: Works with text, numbers, dates, and boolean values
- **Cross-Column**: Single search box searches all columns at once
- **Flexible Matching**: Finds "john" in "John Doe", "123" in "Order #12345", etc.

### 🔧 **User-Friendly Controls**
- **Search Box**: Located next to the CRUD buttons for easy access
- **Placeholder Text**: Shows "Type to search..." as a hint
- **Clear Button**: Quickly reset search and show all data
- **Auto-Clear**: Search clears when switching between tables

## How to Use

### Basic Search
1. **Connect to Database**: Enter connection string and click "Connect & Load Tables"
2. **Select Table**: Choose a table from the dropdown to load data
3. **Start Typing**: Click in the search box and type your search term
4. **See Results**: Data filters instantly as you type

### Search Examples

#### **Search by Name**
- Type: `john`
- Finds: "John Doe", "Johnson", "<EMAIL>"

#### **Search by ID**
- Type: `123`
- Finds: ID 123, Order #12345, Phone 555-1234

#### **Search by Date**
- Type: `2024`
- Finds: All records from 2024, "2024-01-15", etc.

#### **Search by Status**
- Type: `active`
- Finds: All records with "active" status

### Clear Search
- **Click "Clear" Button**: Instantly shows all data
- **Delete All Text**: Manually clear the search box
- **Switch Tables**: Automatically clears search when changing tables

## Technical Details

### How It Works
1. **Data Storage**: Original table data is stored in memory
2. **Real-Time Filtering**: Uses DataView with dynamic row filters
3. **Multi-Column**: Creates OR conditions across all columns
4. **Type Conversion**: Converts all data types to strings for searching
5. **SQL Injection Safe**: Properly escapes search terms

### Performance
- **Fast Filtering**: Uses in-memory DataView for instant results
- **Efficient**: Only loads data once per table
- **Responsive**: No database queries during search
- **Scalable**: Works well with tables containing thousands of records

## UI Layout

```
[Connection String: ________________] [Table: ▼] [Connect & Load Tables]

[Add Record] [Edit Record] [Delete Record]    [Search: ____________] [Clear]

┌─────────────────────────────────────────────────────────────────┐
│                        Data Grid View                           │
│  ID │ Name      │ Email              │ Status │ Created        │
│  1  │ John Doe  │ <EMAIL>   │ Active │ 2024-01-15     │
│  2  │ Jane Smith│ <EMAIL>   │ Active │ 2024-01-16     │
│     │           │                    │        │                │
└─────────────────────────────────────────────────────────────────┘
```

## Benefits

### 🚀 **Improved Productivity**
- **Quick Navigation**: Find records instantly in large tables
- **No Scrolling**: Avoid scrolling through hundreds of rows
- **Multi-Purpose**: Search by any field without complex filters

### 👥 **Better User Experience**
- **Intuitive**: Works like search in modern applications
- **Responsive**: Immediate visual feedback
- **Forgiving**: Partial matches and case-insensitive

### 📊 **Data Management**
- **Efficient Editing**: Quickly find records to edit or delete
- **Data Exploration**: Easily explore data patterns
- **Quality Control**: Find specific data for verification

## Tips for Effective Searching

### **Best Practices**
- **Start Specific**: Begin with unique terms, then broaden if needed
- **Use Partial Terms**: Search "john" instead of full "John Doe"
- **Try Different Approaches**: Search by ID, name, email, or status
- **Clear Between Searches**: Use Clear button for fresh searches

### **Search Strategies**
- **Names**: Use first name, last name, or partial matches
- **IDs**: Type just the numbers without prefixes
- **Dates**: Use year (2024) or month-year (01-2024)
- **Status**: Search for "active", "pending", "completed", etc.

## Troubleshooting

**Search not working?**
- Ensure table data is loaded first
- Check that you've selected a table
- Try clearing search and starting over

**No results found?**
- Check spelling and try partial terms
- Search is case-insensitive but exact spelling matters
- Try searching different columns (ID vs Name vs Email)

**Performance issues?**
- Large tables (10,000+ rows) may have slight delays
- Consider using more specific search terms
- Clear search when not needed to improve scrolling

The search feature makes the MariaDB Manager much more powerful for working with real-world datasets!
