using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace MariaDBManager
{
    public partial class DataEntryForm : Form
    {
        private List<ColumnInfo> columns;
        private Dictionary<string, object> currentValues;
        private Dictionary<string, Control> controls;
        private Button btnSave, btnCancel;
        private TableLayoutPanel tableLayoutPanel;
        private DatabaseHelper dbHelper;
        private string tableName;

        public DataEntryForm(List<ColumnInfo> columns, Dictionary<string, object> currentValues = null, DatabaseHelper dbHelper = null, string tableName = null)
        {
            this.columns = columns;
            this.currentValues = currentValues;
            this.controls = new Dictionary<string, Control>();
            this.dbHelper = dbHelper;
            this.tableName = tableName;

            InitializeComponent();
            CreateControls();

            if (currentValues != null)
            {
                PopulateControls();
                this.Text = "Edit Record";
            }
            else
            {
                this.Text = "Add New Record";
            }
        }

        private void InitializeComponent()
        {
            // Ensure minimum height to accommodate buttons
            int formHeight = Math.Max(300, Math.Min(600, 200 + columns.Count * 40));
            this.Size = new Size(500, formHeight);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Create buttons panel first (at bottom)
            var buttonPanel = new FlowLayoutPanel()
            {
                Height = 60,
                Dock = DockStyle.Bottom,
                FlowDirection = FlowDirection.RightToLeft,
                Padding = new Padding(15),
                WrapContents = false,
                BackColor = SystemColors.Control
            };

            btnCancel = new Button()
            {
                Text = "Cancel",
                Size = new Size(80, 30),
                DialogResult = DialogResult.Cancel,
                UseVisualStyleBackColor = true,
                Margin = new Padding(5)
            };

            btnSave = new Button()
            {
                Text = "Save",
                Size = new Size(80, 30),
                DialogResult = DialogResult.OK,
                UseVisualStyleBackColor = true,
                Margin = new Padding(5)
            };

            buttonPanel.Controls.AddRange(new Control[] { btnCancel, btnSave });

            // Create main table layout panel (fills remaining space)
            tableLayoutPanel = new TableLayoutPanel()
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                ColumnCount = 2,
                AutoSize = false,
                AutoScroll = true
            };

            // Set column styles
            tableLayoutPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 150));
            tableLayoutPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));

            // Add controls to form in correct order
            this.Controls.Add(tableLayoutPanel);
            this.Controls.Add(buttonPanel);

            btnSave.Click += BtnSave_Click;
        }

        private void CreateControls()
        {
            int row = 0;
            
            foreach (var column in columns)
            {
                // Skip auto-increment primary keys when adding new records
                if (currentValues == null && column.IsAutoIncrement && column.IsPrimaryKey)
                    continue;

                // Create label
                var label = new Label()
                {
                    Text = column.Name + ":",
                    TextAlign = ContentAlignment.MiddleLeft,
                    Dock = DockStyle.Fill
                };

                // Create appropriate input control based on column type
                Control inputControl = CreateInputControl(column);
                inputControl.Dock = DockStyle.Fill;
                inputControl.Margin = new Padding(3);

                // Add to table layout
                tableLayoutPanel.Controls.Add(label, 0, row);
                tableLayoutPanel.Controls.Add(inputControl, 1, row);
                tableLayoutPanel.RowStyles.Add(new RowStyle(SizeType.AutoSize));

                // Store control reference
                controls[column.Name] = inputControl;
                
                row++;
            }
        }

        private Control CreateInputControl(ColumnInfo column)
        {
            string dataType = column.Type.ToLower();

            // Handle foreign keys first
            if (column.IsForeignKey && dbHelper != null && !string.IsNullOrEmpty(tableName))
            {
                try
                {
                    var fkInfo = dbHelper.GetForeignKeyInfo(tableName, column.Name);
                    if (fkInfo != null)
                    {
                        var comboBox = new ComboBox()
                        {
                            DropDownStyle = ComboBoxStyle.DropDownList,
                            DisplayMember = "Display",
                            ValueMember = "Value"
                        };

                        // Get foreign key data
                        var fkData = dbHelper.GetForeignKeyData(fkInfo.ReferencedTable, fkInfo.ReferencedColumn);
                        var items = new List<object>();

                        // Add empty option for nullable fields
                        if (column.IsNullable)
                        {
                            items.Add(new { Value = (object)null, Display = "(None)" });
                        }

                        // Add data from referenced table
                        foreach (DataRow row in fkData.Rows)
                        {
                            var value = row[fkInfo.ReferencedColumn];
                            var display = fkData.Columns.Count > 1
                                ? $"{row[fkInfo.ReferencedColumn]} - {row[1]}"
                                : value.ToString();
                            items.Add(new { Value = value, Display = display });
                        }

                        comboBox.DataSource = items;
                        return comboBox;
                    }
                }
                catch (Exception ex)
                {
                    // If foreign key lookup fails, fall back to text box
                    var textBox = new TextBox();
                    textBox.PlaceholderText = $"Foreign key to {column.Name} (lookup failed: {ex.Message})";
                    return textBox;
                }
            }

            // Handle different data types
            if (dataType.Contains("text") || dataType.Contains("longtext"))
            {
                return new TextBox()
                {
                    Multiline = true,
                    Height = 60,
                    ScrollBars = ScrollBars.Vertical
                };
            }
            else if (dataType.Contains("date") || dataType.Contains("time"))
            {
                return new DateTimePicker()
                {
                    Format = DateTimePickerFormat.Custom,
                    CustomFormat = dataType.Contains("date") && dataType.Contains("time") ?
                        "yyyy-MM-dd HH:mm:ss" :
                        dataType.Contains("date") ? "yyyy-MM-dd" : "HH:mm:ss"
                };
            }
            else if (dataType.Contains("bool") || dataType.Contains("bit"))
            {
                return new CheckBox()
                {
                    Text = ""
                };
            }
            else if (dataType.Contains("int") || dataType.Contains("decimal") ||
                     dataType.Contains("float") || dataType.Contains("double"))
            {
                return new NumericUpDown()
                {
                    DecimalPlaces = dataType.Contains("decimal") || dataType.Contains("float") || dataType.Contains("double") ? 2 : 0,
                    Minimum = decimal.MinValue,
                    Maximum = decimal.MaxValue
                };
            }
            else
            {
                return new TextBox();
            }
        }

        private void PopulateControls()
        {
            foreach (var kvp in currentValues)
            {
                if (controls.ContainsKey(kvp.Key) && kvp.Value != null)
                {
                    var control = controls[kvp.Key];
                    
                    if (control is TextBox textBox)
                    {
                        textBox.Text = kvp.Value.ToString();
                    }
                    else if (control is NumericUpDown numericUpDown)
                    {
                        if (decimal.TryParse(kvp.Value.ToString(), out decimal value))
                        {
                            numericUpDown.Value = value;
                        }
                    }
                    else if (control is DateTimePicker dateTimePicker)
                    {
                        if (DateTime.TryParse(kvp.Value.ToString(), out DateTime dateValue))
                        {
                            dateTimePicker.Value = dateValue;
                        }
                    }
                    else if (control is CheckBox checkBox)
                    {
                        if (bool.TryParse(kvp.Value.ToString(), out bool boolValue))
                        {
                            checkBox.Checked = boolValue;
                        }
                        else if (kvp.Value.ToString() == "1")
                        {
                            checkBox.Checked = true;
                        }
                    }
                    else if (control is ComboBox comboBox)
                    {
                        // Find the item with matching value
                        for (int i = 0; i < comboBox.Items.Count; i++)
                        {
                            var item = comboBox.Items[i];
                            var itemValue = item.GetType().GetProperty("Value")?.GetValue(item);
                            if (itemValue?.ToString() == kvp.Value?.ToString())
                            {
                                comboBox.SelectedIndex = i;
                                break;
                            }
                        }
                    }
                }
            }
        }

        public Dictionary<string, object> GetValues()
        {
            var values = new Dictionary<string, object>();
            
            foreach (var kvp in controls)
            {
                var control = kvp.Value;
                object value = null;
                
                if (control is TextBox textBox)
                {
                    value = string.IsNullOrEmpty(textBox.Text) ? null : textBox.Text;
                }
                else if (control is NumericUpDown numericUpDown)
                {
                    value = numericUpDown.Value;
                }
                else if (control is DateTimePicker dateTimePicker)
                {
                    value = dateTimePicker.Value;
                }
                else if (control is CheckBox checkBox)
                {
                    value = checkBox.Checked;
                }
                else if (control is ComboBox comboBox)
                {
                    if (comboBox.SelectedItem != null)
                    {
                        value = comboBox.SelectedItem.GetType().GetProperty("Value")?.GetValue(comboBox.SelectedItem);
                    }
                }

                values[kvp.Key] = value;
            }
            
            return values;
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            // Basic validation
            foreach (var column in columns)
            {
                if (!column.IsNullable && !column.IsAutoIncrement && controls.ContainsKey(column.Name))
                {
                    var control = controls[column.Name];
                    bool isEmpty = false;
                    
                    if (control is TextBox textBox && string.IsNullOrWhiteSpace(textBox.Text))
                        isEmpty = true;
                    
                    if (isEmpty)
                    {
                        MessageBox.Show($"Field '{column.Name}' is required.", "Validation Error", 
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        control.Focus();
                        return;
                    }
                }
            }
        }
    }
}
