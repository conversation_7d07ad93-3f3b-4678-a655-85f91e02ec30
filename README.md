# MariaDB Manager - Windows Forms Application

A Windows Forms application for managing MariaDB databases with full CRUD (Create, Read, Update, Delete) functionality.

## Features

- **Database Connection**: Connect to any MariaDB database using connection strings
- **Table Management**: View and select from available tables
- **Data Viewing**: Display table data in a user-friendly grid
- **Real-Time Search**: Filter data as you type across all columns
- **CRUD Operations**:
  - **Create**: Add new records with appropriate input controls
  - **Read**: View all records in a table
  - **Update**: Edit existing records
  - **Delete**: Remove records with confirmation

## Requirements

- .NET 8.0 or later
- MariaDB server
- Visual Studio 2022 or later (recommended)

## Setup Instructions

1. **Clone or download** this project to your local machine

2. **Install MariaDB** if not already installed:

   - Download from: https://mariadb.org/download/
   - Or use XAMPP/WAMP which includes MariaDB

3. **Create a test database** (optional):

   ```sql
   CREATE DATABASE testdb;
   USE testdb;

   CREATE TABLE users (
       id INT AUTO_INCREMENT PRIMARY KEY,
       name VARCHAR(100) NOT NULL,
       email VARCHAR(100) UNIQUE,
       age INT,
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
   );

   INSERT INTO users (name, email, age) VALUES
   ('John Doe', '<EMAIL>', 30),
   ('Jane Smith', '<EMAIL>', 25);
   ```

4. **Build and run** the application:
   ```bash
   dotnet build
   dotnet run
   ```

## Usage

1. **Enter Connection String**:

   - Default format: `Server=localhost;Database=testdb;Uid=root;Pwd=password;`
   - Modify according to your MariaDB setup

2. **Connect**: Click "Connect & Load Tables" to establish connection and load available tables

3. **Select Table**: Choose a table from the dropdown to view its data

4. **Manage Records**:
   - **Add**: Click "Add Record" to create new entries
   - **Edit**: Select a row and click "Edit Record" to modify
   - **Delete**: Select a row and click "Delete Record" to remove

## Connection String Examples

- **Local MariaDB**: `Server=localhost;Database=mydb;Uid=root;Pwd=mypassword;`
- **Remote MariaDB**: `Server=*************;Database=mydb;Uid=username;Pwd=password;Port=3306;`
- **With SSL**: `Server=localhost;Database=mydb;Uid=root;Pwd=password;SslMode=Required;`

## Supported Data Types

The application automatically creates appropriate input controls for different MariaDB data types:

- **Text fields**: VARCHAR, TEXT, LONGTEXT
- **Numeric fields**: INT, DECIMAL, FLOAT, DOUBLE
- **Date/Time fields**: DATE, TIME, DATETIME, TIMESTAMP
- **Boolean fields**: BOOLEAN, BIT
- **Multi-line text**: TEXT, LONGTEXT

## Error Handling

- Connection errors are displayed with helpful messages
- Input validation prevents invalid data entry
- SQL errors are caught and displayed to the user
- Confirmation dialogs for destructive operations (delete)

## Security Notes

- Always use strong passwords for database connections
- Consider using environment variables for sensitive connection details
- Test with non-production databases first
- Regular backups are recommended before bulk operations

## Troubleshooting

**Connection Issues**:

- Verify MariaDB service is running
- Check firewall settings
- Confirm connection string parameters
- Ensure user has proper database permissions

**Build Issues**:

- Ensure .NET 8.0 SDK is installed
- Restore NuGet packages: `dotnet restore`
- Clean and rebuild: `dotnet clean && dotnet build`

## License

This project is provided as-is for educational and development purposes.
