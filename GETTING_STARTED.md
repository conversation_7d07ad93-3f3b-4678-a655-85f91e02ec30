# Getting Started with MariaDB Manager

## Quick Start Guide

### 1. Prerequisites

- **MariaDB Server**: Install MariaDB or use XAMPP/WAMP
- **.NET 8.0**: Download from https://dotnet.microsoft.com/download
- **Visual Studio**: 2022 or later (recommended) or VS Code

### 2. Database Setup

Run the provided `sample_database.sql` script to create test data:

```bash
# Connect to MariaDB
mysql -u root -p

# Run the script
source sample_database.sql
```

Or manually create a database:

```sql
CREATE DATABASE testdb;
USE testdb;

CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE,
    age INT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3. Running the Application

#### Option A: Using Visual Studio

1. Open `MariaDBManager.csproj` in Visual Studio
2. Press F5 to build and run

#### Option B: Using Command Line

```bash
# Build the project
dotnet build

# Run the application
dotnet run
```

### 4. First Connection

1. **Enter Connection String**:

   ```
   Server=localhost;Database=testdb;Uid=root;Pwd=yourpassword;
   ```

2. **Click "Connect & Load Tables"**: This will:

   - Test the database connection
   - Load available tables into the dropdown

3. **Select a Table**: Choose from the dropdown to view data

### 5. Using CRUD Operations

#### Adding Records

1. Select a table from the dropdown
2. Click "Add Record"
3. Fill in the form fields (the Save and Cancel buttons are at the bottom)
4. Click "Save" to add the record or "Cancel" to abort

#### Editing Records

1. Select a row in the data grid
2. Click "Edit Record"
3. Modify the values in the form
4. Click "Save"

#### Deleting Records

1. Select a row in the data grid
2. Click "Delete Record"
3. Confirm the deletion

### 6. Common Connection Strings

**Local Development:**

```
Server=localhost;Database=testdb;Uid=root;Pwd=password;
```

**XAMPP/WAMP:**

```
Server=localhost;Database=testdb;Uid=root;Pwd=;Port=3306;
```

**Remote Server:**

```
Server=*************;Database=mydb;Uid=username;Pwd=password;Port=3306;
```

**With SSL:**

```
Server=localhost;Database=testdb;Uid=root;Pwd=password;SslMode=Required;
```

### 7. Troubleshooting

**"Connection failed" Error:**

- Check if MariaDB service is running
- Verify connection string parameters
- Ensure user has database permissions
- Check firewall settings

**"No tables found":**

- Verify the database exists and has tables
- Check user permissions to view tables
- Ensure correct database name in connection string

**Build Errors:**

- Ensure .NET 8.0 SDK is installed
- Run `dotnet restore` to restore packages
- Check that all files are present

### 8. Features Overview

- **Dynamic Table Loading**: Automatically discovers and loads all tables
- **Smart Input Controls**: Creates appropriate controls based on column types
- **Auto-increment Handling**: Skips auto-increment fields when adding records
- **Data Type Support**: Handles text, numbers, dates, booleans
- **Error Handling**: User-friendly error messages
- **Validation**: Prevents invalid data entry

### 9. Next Steps

- Customize the UI for your specific needs
- Add additional validation rules
- Implement data export functionality
- Add search and filtering capabilities
- Create custom reports

For more detailed information, see the main README.md file.
