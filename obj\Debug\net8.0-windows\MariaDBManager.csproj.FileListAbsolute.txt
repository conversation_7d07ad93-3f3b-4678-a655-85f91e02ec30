C:\Users\<USER>\Documents\augment-projects\vvwp-app\bin\Debug\net8.0-windows\MariaDBManager.exe
C:\Users\<USER>\Documents\augment-projects\vvwp-app\bin\Debug\net8.0-windows\MariaDBManager.deps.json
C:\Users\<USER>\Documents\augment-projects\vvwp-app\bin\Debug\net8.0-windows\MariaDBManager.runtimeconfig.json
C:\Users\<USER>\Documents\augment-projects\vvwp-app\bin\Debug\net8.0-windows\MariaDBManager.dll
C:\Users\<USER>\Documents\augment-projects\vvwp-app\bin\Debug\net8.0-windows\MariaDBManager.pdb
C:\Users\<USER>\Documents\augment-projects\vvwp-app\bin\Debug\net8.0-windows\BouncyCastle.Cryptography.dll
C:\Users\<USER>\Documents\augment-projects\vvwp-app\bin\Debug\net8.0-windows\Google.Protobuf.dll
C:\Users\<USER>\Documents\augment-projects\vvwp-app\bin\Debug\net8.0-windows\K4os.Compression.LZ4.dll
C:\Users\<USER>\Documents\augment-projects\vvwp-app\bin\Debug\net8.0-windows\K4os.Compression.LZ4.Streams.dll
C:\Users\<USER>\Documents\augment-projects\vvwp-app\bin\Debug\net8.0-windows\K4os.Hash.xxHash.dll
C:\Users\<USER>\Documents\augment-projects\vvwp-app\bin\Debug\net8.0-windows\MySql.Data.dll
C:\Users\<USER>\Documents\augment-projects\vvwp-app\bin\Debug\net8.0-windows\System.IO.Pipelines.dll
C:\Users\<USER>\Documents\augment-projects\vvwp-app\bin\Debug\net8.0-windows\ZstdSharp.dll
C:\Users\<USER>\Documents\augment-projects\vvwp-app\bin\Debug\net8.0-windows\runtimes\win-x64\native\comerr64.dll
C:\Users\<USER>\Documents\augment-projects\vvwp-app\bin\Debug\net8.0-windows\runtimes\win-x64\native\gssapi64.dll
C:\Users\<USER>\Documents\augment-projects\vvwp-app\bin\Debug\net8.0-windows\runtimes\win-x64\native\k5sprt64.dll
C:\Users\<USER>\Documents\augment-projects\vvwp-app\bin\Debug\net8.0-windows\runtimes\win-x64\native\krb5_64.dll
C:\Users\<USER>\Documents\augment-projects\vvwp-app\bin\Debug\net8.0-windows\runtimes\win-x64\native\krbcc64.dll
C:\Users\<USER>\Documents\augment-projects\vvwp-app\obj\Debug\net8.0-windows\MariaDBManager.csproj.AssemblyReference.cache
C:\Users\<USER>\Documents\augment-projects\vvwp-app\obj\Debug\net8.0-windows\MariaDBManager.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Documents\augment-projects\vvwp-app\obj\Debug\net8.0-windows\MariaDBManager.AssemblyInfoInputs.cache
C:\Users\<USER>\Documents\augment-projects\vvwp-app\obj\Debug\net8.0-windows\MariaDBManager.AssemblyInfo.cs
C:\Users\<USER>\Documents\augment-projects\vvwp-app\obj\Debug\net8.0-windows\MariaDBManager.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Documents\augment-projects\vvwp-app\obj\Debug\net8.0-windows\MariaDBM.28FAECEB.Up2Date
C:\Users\<USER>\Documents\augment-projects\vvwp-app\obj\Debug\net8.0-windows\MariaDBManager.dll
C:\Users\<USER>\Documents\augment-projects\vvwp-app\obj\Debug\net8.0-windows\refint\MariaDBManager.dll
C:\Users\<USER>\Documents\augment-projects\vvwp-app\obj\Debug\net8.0-windows\MariaDBManager.pdb
C:\Users\<USER>\Documents\augment-projects\vvwp-app\obj\Debug\net8.0-windows\MariaDBManager.genruntimeconfig.cache
C:\Users\<USER>\Documents\augment-projects\vvwp-app\obj\Debug\net8.0-windows\ref\MariaDBManager.dll
