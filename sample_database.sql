-- Sample database setup for MariaDB Manager testing
-- Run this script in your MariaDB server to create test data

CREATE DATABASE IF NOT EXISTS testdb;
USE testdb;

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE,
    age INT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Products table
CREATE TABLE IF NOT EXISTS products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    stock_quantity INT DEFAULT 0,
    category VARCHAR(50),
    is_available BOOLEAN DEFAULT TRUE,
    created_date DATE DEFAULT (CURRENT_DATE)
);

-- Orders table
CREATE TABLE IF NOT EXISTS orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    order_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    total_amount DECIMAL(10,2),
    status VARCHAR(20) DEFAULT 'pending',
    notes TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Insert sample data
INSERT INTO users (name, email, age, is_active) VALUES 
('John Doe', '<EMAIL>', 30, TRUE),
('Jane Smith', '<EMAIL>', 25, TRUE),
('Bob Johnson', '<EMAIL>', 35, FALSE),
('Alice Brown', '<EMAIL>', 28, TRUE),
('Charlie Wilson', '<EMAIL>', 42, TRUE);

INSERT INTO products (name, description, price, stock_quantity, category, is_available) VALUES 
('Laptop Computer', 'High-performance laptop for work and gaming', 999.99, 15, 'Electronics', TRUE),
('Wireless Mouse', 'Ergonomic wireless mouse with long battery life', 29.99, 50, 'Electronics', TRUE),
('Office Chair', 'Comfortable ergonomic office chair', 199.99, 8, 'Furniture', TRUE),
('Coffee Mug', 'Ceramic coffee mug with company logo', 12.99, 100, 'Accessories', TRUE),
('Notebook', 'Spiral-bound notebook for taking notes', 5.99, 200, 'Stationery', TRUE);

INSERT INTO orders (user_id, total_amount, status, notes) VALUES 
(1, 1029.98, 'completed', 'Laptop and mouse bundle'),
(2, 199.99, 'pending', 'Office chair for home office'),
(3, 18.98, 'shipped', 'Coffee mug and notebook'),
(1, 29.99, 'completed', 'Additional wireless mouse'),
(4, 999.99, 'processing', 'Laptop for new employee');

-- Display table information
SELECT 'Users Table' as Info;
SELECT COUNT(*) as Total_Users FROM users;

SELECT 'Products Table' as Info;
SELECT COUNT(*) as Total_Products FROM products;

SELECT 'Orders Table' as Info;
SELECT COUNT(*) as Total_Orders FROM orders;
