using System;
using System.Collections.Generic;
using System.Data;
using MySql.Data.MySqlClient;

namespace MariaDBManager
{
    public class DatabaseHelper
    {
        public string ConnectionString { get; set; }

        public DatabaseHelper()
        {
            ConnectionString = "";
        }

        public DatabaseHelper(string connectionString)
        {
            ConnectionString = connectionString;
        }

        private MySqlConnection GetConnection()
        {
            return new MySqlConnection(ConnectionString);
        }

        public List<string> GetTables()
        {
            var tables = new List<string>();

            try
            {
                using (var connection = GetConnection())
                {
                    connection.Open();
                    var command = new MySqlCommand("SHOW TABLES", connection);

                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            tables.Add(reader.GetString(0));
                        }
                    }
                }
            }
            catch (MySqlException ex)
            {
                throw new Exception($"Database error while retrieving tables: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error retrieving tables: {ex.Message}", ex);
            }

            return tables;
        }

        public DataTable GetTableData(string tableName)
        {
            if (string.IsNullOrWhiteSpace(tableName))
                throw new ArgumentException("Table name cannot be empty", nameof(tableName));

            var dataTable = new DataTable();

            try
            {
                using (var connection = GetConnection())
                {
                    connection.Open();
                    var command = new MySqlCommand($"SELECT * FROM `{tableName}`", connection);
                    var adapter = new MySqlDataAdapter(command);
                    adapter.Fill(dataTable);
                }
            }
            catch (MySqlException ex)
            {
                throw new Exception($"Database error while retrieving data from table '{tableName}': {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error retrieving data from table '{tableName}': {ex.Message}", ex);
            }

            return dataTable;
        }

        public List<ColumnInfo> GetTableColumns(string tableName)
        {
            var columns = new List<ColumnInfo>();
            
            using (var connection = GetConnection())
            {
                connection.Open();
                var command = new MySqlCommand($"DESCRIBE `{tableName}`", connection);
                
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        var column = new ColumnInfo
                        {
                            Name = reader.GetString("Field"),
                            Type = reader.GetString("Type"),
                            IsNullable = reader.GetString("Null") == "YES",
                            Key = reader.GetString("Key"),
                            Default = reader.IsDBNull("Default") ? null : reader.GetString("Default"),
                            Extra = reader.GetString("Extra")
                        };
                        columns.Add(column);
                    }
                }
            }
            
            return columns;
        }

        public string GetPrimaryKey(string tableName)
        {
            using (var connection = GetConnection())
            {
                connection.Open();
                var command = new MySqlCommand($"DESCRIBE `{tableName}`", connection);
                
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        if (reader.GetString("Key") == "PRI")
                        {
                            return reader.GetString("Field");
                        }
                    }
                }
            }
            
            throw new Exception($"No primary key found for table {tableName}");
        }

        public void InsertRecord(string tableName, Dictionary<string, object> values)
        {
            var columns = string.Join(", ", values.Keys.Select(k => $"`{k}`"));
            var parameters = string.Join(", ", values.Keys.Select(k => $"@{k}"));
            
            var sql = $"INSERT INTO `{tableName}` ({columns}) VALUES ({parameters})";
            
            using (var connection = GetConnection())
            {
                connection.Open();
                var command = new MySqlCommand(sql, connection);
                
                foreach (var kvp in values)
                {
                    command.Parameters.AddWithValue($"@{kvp.Key}", kvp.Value ?? DBNull.Value);
                }
                
                command.ExecuteNonQuery();
            }
        }

        public void UpdateRecord(string tableName, string primaryKey, object keyValue, Dictionary<string, object> values)
        {
            var setClause = string.Join(", ", values.Keys.Select(k => $"`{k}` = @{k}"));
            var sql = $"UPDATE `{tableName}` SET {setClause} WHERE `{primaryKey}` = @keyValue";
            
            using (var connection = GetConnection())
            {
                connection.Open();
                var command = new MySqlCommand(sql, connection);
                
                foreach (var kvp in values)
                {
                    command.Parameters.AddWithValue($"@{kvp.Key}", kvp.Value ?? DBNull.Value);
                }
                command.Parameters.AddWithValue("@keyValue", keyValue);
                
                command.ExecuteNonQuery();
            }
        }

        public void DeleteRecord(string tableName, string primaryKey, object keyValue)
        {
            var sql = $"DELETE FROM `{tableName}` WHERE `{primaryKey}` = @keyValue";
            
            using (var connection = GetConnection())
            {
                connection.Open();
                var command = new MySqlCommand(sql, connection);
                command.Parameters.AddWithValue("@keyValue", keyValue);
                command.ExecuteNonQuery();
            }
        }

        public bool TestConnection()
        {
            try
            {
                using (var connection = GetConnection())
                {
                    connection.Open();
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }
    }

    public class ColumnInfo
    {
        public string Name { get; set; }
        public string Type { get; set; }
        public bool IsNullable { get; set; }
        public string Key { get; set; }
        public string Default { get; set; }
        public string Extra { get; set; }
        
        public bool IsPrimaryKey => Key == "PRI";
        public bool IsAutoIncrement => Extra.Contains("auto_increment");
    }
}
