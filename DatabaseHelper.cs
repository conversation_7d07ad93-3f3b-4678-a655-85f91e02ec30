using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using MySql.Data.MySqlClient;

namespace MariaDBManager
{
    public class DatabaseHelper
    {
        public string ConnectionString { get; set; }

        public DatabaseHelper()
        {
            ConnectionString = "";
        }

        public DatabaseHelper(string connectionString)
        {
            ConnectionString = connectionString;
        }

        private MySqlConnection GetConnection()
        {
            return new MySqlConnection(ConnectionString);
        }

        public List<string> GetTables()
        {
            var tables = new List<string>();

            try
            {
                using (var connection = GetConnection())
                {
                    connection.Open();
                    var command = new MySqlCommand("SHOW TABLES", connection);

                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            tables.Add(reader.GetString(0));
                        }
                    }
                }
            }
            catch (MySqlException ex)
            {
                throw new Exception($"Database error while retrieving tables: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error retrieving tables: {ex.Message}", ex);
            }

            return tables;
        }

        public DataTable GetTableData(string tableName)
        {
            if (string.IsNullOrWhiteSpace(tableName))
                throw new ArgumentException("Table name cannot be empty", nameof(tableName));

            var dataTable = new DataTable();

            try
            {
                using (var connection = GetConnection())
                {
                    connection.Open();
                    var command = new MySqlCommand($"SELECT * FROM `{tableName}`", connection);
                    var adapter = new MySqlDataAdapter(command);
                    adapter.Fill(dataTable);
                }
            }
            catch (MySqlException ex)
            {
                throw new Exception($"Database error while retrieving data from table '{tableName}': {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error retrieving data from table '{tableName}': {ex.Message}", ex);
            }

            return dataTable;
        }

        public List<ColumnInfo> GetTableColumns(string tableName)
        {
            var columns = new List<ColumnInfo>();
            
            using (var connection = GetConnection())
            {
                connection.Open();
                var command = new MySqlCommand($"DESCRIBE `{tableName}`", connection);
                
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        var column = new ColumnInfo
                        {
                            Name = reader.GetString("Field"),
                            Type = reader.GetString("Type"),
                            IsNullable = reader.GetString("Null") == "YES",
                            Key = reader.GetString("Key"),
                            Default = reader.IsDBNull("Default") ? null : reader.GetString("Default"),
                            Extra = reader.GetString("Extra")
                        };
                        columns.Add(column);
                    }
                }
            }
            
            return columns;
        }

        public string GetPrimaryKey(string tableName)
        {
            using (var connection = GetConnection())
            {
                connection.Open();
                var command = new MySqlCommand($"DESCRIBE `{tableName}`", connection);
                
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        if (reader.GetString("Key") == "PRI")
                        {
                            return reader.GetString("Field");
                        }
                    }
                }
            }
            
            throw new Exception($"No primary key found for table {tableName}");
        }

        public void InsertRecord(string tableName, Dictionary<string, object> values)
        {
            var columns = string.Join(", ", values.Keys.Select(k => $"`{k}`"));
            var parameters = string.Join(", ", values.Keys.Select(k => $"@{k}"));
            
            var sql = $"INSERT INTO `{tableName}` ({columns}) VALUES ({parameters})";
            
            using (var connection = GetConnection())
            {
                connection.Open();
                var command = new MySqlCommand(sql, connection);
                
                foreach (var kvp in values)
                {
                    command.Parameters.AddWithValue($"@{kvp.Key}", kvp.Value ?? DBNull.Value);
                }
                
                command.ExecuteNonQuery();
            }
        }

        public void UpdateRecord(string tableName, string primaryKey, object keyValue, Dictionary<string, object> values)
        {
            var setClause = string.Join(", ", values.Keys.Select(k => $"`{k}` = @{k}"));
            var sql = $"UPDATE `{tableName}` SET {setClause} WHERE `{primaryKey}` = @keyValue";
            
            using (var connection = GetConnection())
            {
                connection.Open();
                var command = new MySqlCommand(sql, connection);
                
                foreach (var kvp in values)
                {
                    command.Parameters.AddWithValue($"@{kvp.Key}", kvp.Value ?? DBNull.Value);
                }
                command.Parameters.AddWithValue("@keyValue", keyValue);
                
                command.ExecuteNonQuery();
            }
        }

        public void DeleteRecord(string tableName, string primaryKey, object keyValue)
        {
            var sql = $"DELETE FROM `{tableName}` WHERE `{primaryKey}` = @keyValue";
            
            using (var connection = GetConnection())
            {
                connection.Open();
                var command = new MySqlCommand(sql, connection);
                command.Parameters.AddWithValue("@keyValue", keyValue);
                command.ExecuteNonQuery();
            }
        }

        public bool TestConnection()
        {
            try
            {
                using (var connection = GetConnection())
                {
                    connection.Open();
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }

        public ForeignKeyInfo GetForeignKeyInfo(string tableName, string columnName)
        {
            try
            {
                using (var connection = GetConnection())
                {
                    connection.Open();
                    var sql = @"
                        SELECT
                            REFERENCED_TABLE_NAME,
                            REFERENCED_COLUMN_NAME
                        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
                        WHERE TABLE_SCHEMA = DATABASE()
                        AND TABLE_NAME = @tableName
                        AND COLUMN_NAME = @columnName
                        AND REFERENCED_TABLE_NAME IS NOT NULL";

                    var command = new MySqlCommand(sql, connection);
                    command.Parameters.AddWithValue("@tableName", tableName);
                    command.Parameters.AddWithValue("@columnName", columnName);

                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new ForeignKeyInfo
                            {
                                ReferencedTable = reader.GetString("REFERENCED_TABLE_NAME"),
                                ReferencedColumn = reader.GetString("REFERENCED_COLUMN_NAME")
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error getting foreign key info: {ex.Message}", ex);
            }

            return null;
        }

        public DataTable GetForeignKeyData(string tableName, string keyColumn, string displayColumn = null)
        {
            try
            {
                using (var connection = GetConnection())
                {
                    connection.Open();

                    // If no display column specified, try to find a suitable one
                    if (string.IsNullOrEmpty(displayColumn))
                    {
                        var columns = GetTableColumns(tableName);
                        displayColumn = columns.FirstOrDefault(c =>
                            c.Name.ToLower().Contains("name") ||
                            c.Name.ToLower().Contains("title") ||
                            c.Name.ToLower().Contains("description"))?.Name ?? keyColumn;
                    }

                    var sql = displayColumn == keyColumn
                        ? $"SELECT `{keyColumn}` FROM `{tableName}` ORDER BY `{keyColumn}`"
                        : $"SELECT `{keyColumn}`, `{displayColumn}` FROM `{tableName}` ORDER BY `{displayColumn}`";

                    var command = new MySqlCommand(sql, connection);
                    var adapter = new MySqlDataAdapter(command);
                    var dataTable = new DataTable();
                    adapter.Fill(dataTable);

                    return dataTable;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error getting foreign key data: {ex.Message}", ex);
            }
        }
    }

    public class ColumnInfo
    {
        public string Name { get; set; }
        public string Type { get; set; }
        public bool IsNullable { get; set; }
        public string Key { get; set; }
        public string Default { get; set; }
        public string Extra { get; set; }

        public bool IsPrimaryKey => Key == "PRI";
        public bool IsAutoIncrement => Extra.Contains("auto_increment");
        public bool IsForeignKey => Key == "MUL";
    }

    public class ForeignKeyInfo
    {
        public string ReferencedTable { get; set; } = string.Empty;
        public string ReferencedColumn { get; set; } = string.Empty;
    }
}
