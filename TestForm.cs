using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;

namespace MariaDBManager
{
    public partial class TestForm : Form
    {
        public TestForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "Test Form - Button Layout";
            this.Size = new Size(400, 300);
            this.StartPosition = FormStartPosition.CenterScreen;

            // Create buttons panel at bottom
            var buttonPanel = new FlowLayoutPanel()
            {
                Height = 50,
                Dock = DockStyle.Bottom,
                FlowDirection = FlowDirection.RightToLeft,
                Padding = new Padding(10),
                WrapContents = false,
                BackColor = Color.LightGray
            };

            var btnCancel = new Button()
            {
                Text = "Cancel",
                Size = new Size(80, 30),
                DialogResult = DialogResult.Cancel,
                UseVisualStyleBackColor = true,
                Margin = new Padding(5)
            };

            var btnSave = new Button()
            {
                Text = "Save",
                Size = new Size(80, 30),
                DialogResult = DialogResult.OK,
                UseVisualStyleBackColor = true,
                Margin = new Padding(5)
            };

            buttonPanel.Controls.AddRange(new Control[] { btnCancel, btnSave });

            // Create main content area
            var mainPanel = new Panel()
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(10)
            };

            var label = new Label()
            {
                Text = "This is a test form to verify button layout.\nThe Save and Cancel buttons should be visible at the bottom.",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter
            };

            mainPanel.Controls.Add(label);

            // Add controls to form
            this.Controls.Add(mainPanel);
            this.Controls.Add(buttonPanel);
        }
    }
}
